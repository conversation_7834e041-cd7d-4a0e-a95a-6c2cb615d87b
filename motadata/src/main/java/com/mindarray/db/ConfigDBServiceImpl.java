/*
 *   Copyright (c) Motadata 2025. All rights reserved.
 *
 *   This source code is the property of Motadata and constitutes
 *   proprietary and confidential information. Unauthorized copying, distribution,
 *   modification, or use of this file, via any medium, is strictly prohibited
 *   unless prior written permission is obtained from Motadata.
 *
 *   Unauthorized access or use of this software may result in legal action
 *   and/or prosecution to the fullest extent of the law.
 *
 *   This software is provided "AS IS," without warranties of any kind, express
 *   or implied, including but not limited to implied warranties of
 *   merchantability or fitness for a particular purpose. In no event shall
 *   Motadata be held liable for any damages arising from the use
 *   of this software.
 *
 *   For inquiries, contact: <EMAIL>
 *
 */

/*
 *     Change Logs:
 *     Date            Author          Notes
 *     24-Jun-2025	   Pruthvi		  MOTADATA-6580 : Encryptionn/decryption added for sensitive field before adding it.
 */

package com.mindarray.db;

import com.mindarray.GlobalConstants;
import com.mindarray.api.BackupProfile;
import com.mindarray.ha.HAConstants;
import com.mindarray.util.CipherUtil;
import com.mindarray.util.CommonUtil;
import com.mindarray.util.Logger;
import io.vertx.core.*;
import io.vertx.core.json.JsonArray;
import io.vertx.core.json.JsonObject;
import io.vertx.sqlclient.Pool;
import io.vertx.sqlclient.Tuple;

import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.IntStream;

import static com.mindarray.GlobalConstants.*;
import static com.mindarray.api.APIConstants.*;
import static com.mindarray.api.User.USER_NAME;
import static com.mindarray.eventbus.EventBusConstants.EVENT_AUDIT;

/**
 * Implementation of ConfigDBService that provides database operations for configuration data.
 *
 * <p>This service handles all CRUD operations for configuration data stored in PostgreSQL,
 * including support for compliance-related operations, backup functionality, and high availability
 * synchronization. The service uses Vert.x reactive patterns for asynchronous database operations.</p>
 *
 * <p>Key features:</p>
 * <ul>
 *   <li>Standard CRUD operations (save, get, update, delete)</li>
 *   <li>Batch operations for improved performance</li>
 *   <li>Compliance-specific database operations</li>
 *   <li>Database backup and restore functionality</li>
 *   <li>High availability synchronization</li>
 *   <li>Audit logging for all operations</li>
 * </ul>
 *
 * <p>All operations support audit logging with user and remote IP tracking for compliance
 * and security purposes. The service automatically handles table creation, data validation,
 * and error handling.</p>
 *
 * <AUTHOR> Engineering Team
 * @since 8.0.0
 */
public class ConfigDBServiceImpl implements ConfigDBService
{
    private static final Logger LOGGER = new Logger(ConfigDBServiceImpl.class, GlobalConstants.MOTADATA_DB, "Config DB Service");
    private static final String RECORD = "record";
    private final Vertx vertx;
    private final Pool db;
    private final CipherUtil cipherUtil;

    /**
     * Creates a new ConfigDBService implementation with PostgreSQL connection pool.
     *
     * <p>This constructor initializes the database connection pool, creates required tables,
     * and sets up compliance-related database structures. In development mode, it will
     * clear the existing database schema for testing purposes.</p>
     *
     * @param vertx   The Vert.x instance for asynchronous operations
     * @param handler Handler to receive the initialization result
     */
    ConfigDBServiceImpl(Vertx vertx, Pool db, Handler<AsyncResult<ConfigDBService>> handler)
    {
        this.vertx = vertx;

        this.db = db;

        this.cipherUtil = new CipherUtil();

        try
        {
            // Create required system tables if they don't exist
            db.query(String.format("CREATE TABLE IF NOT EXISTS %s (id BIGINT PRIMARY KEY, record JSONB)", DBConstants.TBL_SYSTEM)).execute()
                    .onComplete(future ->
                    {
                        if (future.succeeded())
                        {
                            LOGGER.info("system table created successfully!");

                            handler.handle(Future.succeededFuture(this));
                        }
                        else
                        {
                            LOGGER.error(future.cause());

                            handler.handle(Future.failedFuture(future.cause()));
                        }
                    });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            handler.handle(Future.failedFuture(exception));
        }
    }

    /**
     * Saves a single record to the specified table.
     *
     * <p>This method creates the table if it doesn't exist and inserts the record
     * with automatic ID generation if not provided. The operation includes audit
     * logging and high availability synchronization.</p>
     *
     * @param table    The name of the table to save the record to
     * @param record   The JSON record to save
     * @param user     The username performing the operation for audit purposes
     * @param remoteIP The remote IP address for audit and security tracking
     * @return Future containing the generated record ID or error
     */
    @Override
    public Future<Long> save(String table, JsonObject record, String user, String remoteIP)
    {
        return saveAll(table, new JsonArray().add(record), user, remoteIP)
            .map(result -> result.getLong(0));
    }

    /**
     * Saves multiple records to the specified table in a batch operation.
     *
     * <p>This method provides efficient batch insertion of multiple records.
     * It creates the table if it doesn't exist, assigns IDs to records that
     * don't have them, and performs all insertions in a single database transaction
     * for better performance.</p>
     *
     * @param table    The name of the table to save records to
     * @param records  Array of JSON records to save
     * @param user     The username performing the operation for audit purposes
     * @param remoteIP The remote IP address for audit and security tracking
     * @return Future containing array of generated record IDs or error
     */
    @Override
    public Future<JsonArray> saveAll(String table, JsonArray records, String user, String remoteIP)
    {
        var createdItems = new JsonArray();

        var promise = Promise.<JsonArray>promise();

        // Create table if it doesn't exist
        db.query("CREATE TABLE IF NOT EXISTS " + table + " (id BIGINT PRIMARY KEY, record JSONB)").execute().onComplete(result ->
        {
            try
            {
                if (result.succeeded())
                {
                    var recordIds = new JsonArray();

                    var tuples = new ArrayList<Tuple>();

                    // Prepare batch insert with ID assignment
                    for (var index = 0; index < records.size(); index++)
                    {
                        var record = records.getJsonObject(index);

                        // Set default entity type if not specified
                        if (!record.containsKey(DBConstants.FIELD_TYPE))
                        {
                            record.put(DBConstants.FIELD_TYPE, DBConstants.ENTITY_TYPE_USER);
                        }

                        // Generate ID if not provided
                        var id = record.containsKey(GlobalConstants.ID) ? record.getLong(GlobalConstants.ID) : CommonUtil.newId();

                        tuples.add(Tuple.of(id, record.put(GlobalConstants.ID, id)));

                        recordIds.add(id);

                        if (!remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                        {
                            CommonUtil.encryptSensitiveFields(record, cipherUtil);
                        }

                        createdItems.add(record);
                    }

                    // Execute batch insert if there are records to save
                    if (!tuples.isEmpty())
                    {
                        db.preparedQuery("INSERT INTO " + table + " (id, record) VALUES ($1, $2)")
                                .executeBatch(tuples)
                                .onComplete( asyncResult ->
                                {
                                    try
                                    {
                                        if (asyncResult.succeeded())
                                        {
                                            promise.complete(recordIds);

                                            // Sync with HA cluster if not system or observer operation
                                            if (!remoteIP.equalsIgnoreCase(MOTADATA_SYSTEM) && !remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                                            {
                                                HAConstants.sync(table, createdItems, user, HAConstants.HASyncOperation.SAVE_ALL.getName(), BackupProfile.BackupProfileType.CONFIG_DB.getName());
                                            }

                                            // Send audit notifications for each created item
                                            if (!remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                                            {
                                                for (var index = 0; index < createdItems.size(); index++)
                                                {
                                                    notify(remoteIP, user, REQUEST_CREATE, table, Boolean.TRUE, null, null, createdItems.getJsonObject(index));
                                                }
                                            }
                                        }
                                        else
                                        {
                                            LOGGER.error(asyncResult.cause());

                                            promise.fail(asyncResult.cause());

                                            notify(remoteIP, user, REQUEST_CREATE, table, Boolean.FALSE, asyncResult.cause().getMessage(), null, null);
                                        }
                                    }
                                    catch (Exception exception)
                                    {
                                        LOGGER.error(exception);

                                        promise.fail(exception);

                                        notify(remoteIP, user, REQUEST_CREATE, table, Boolean.FALSE, exception.getMessage(), null, null);
                                    }
                                });
                    }
                    else
                    {
                        LOGGER.debug("request received to insert empty data");

                        promise.complete(recordIds);
                    }
                }
                else
                {
                    LOGGER.error(result.cause());

                    promise.fail(result.cause());

                    notify(remoteIP, user, REQUEST_CREATE, table, Boolean.FALSE, result.cause().getMessage(), null, null);
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                promise.fail(exception);

                notify(remoteIP, user, REQUEST_CREATE, table, Boolean.FALSE, exception.getMessage(), null, null);
            }

        });

        return promise.future();
    }

    @Override
    public Future<JsonArray> delete(String table, JsonObject filter, String user, String remoteIP)
    {
        return deleteAll(table, filter, user, remoteIP);
    }

    @Override
    public Future<JsonArray> deleteAll(String table, JsonObject filter, String user, String remoteIP)
    {
        var promise = Promise.<JsonArray>promise();

        try
        {
            var items = new JsonArray();

            var records = new JsonArray();

            var query = new StringBuilder();

            // Build delete query with system entity protection
            query.append("DELETE FROM ").append(table).append(" WHERE ").append(filter.getString(DBConstants.FIELD_NAME).equalsIgnoreCase(GlobalConstants.ID) ? "id" : String.format("record->>'%s' != '%s' AND record->>'%s'", DBConstants.FIELD_TYPE, DBConstants.ENTITY_TYPE_SYSTEM, filter.getString(DBConstants.FIELD_NAME)));

            var tuple = buildTuple(query, filter);

            query.append(" RETURNING *");

            db.preparedQuery(query.toString())
                    .execute(tuple)
                    .onComplete(result ->
                    {
                        try
                        {
                            if (result.succeeded())
                            {
                                // Collect deleted records and their IDs
                                for (var row : result.result())
                                {
                                    items.add(row.getJsonObject(RECORD));

                                    records.add(row.getLong(ID));
                                }

                                // Sync with HA cluster if not system or observer operation
                                if (!remoteIP.equalsIgnoreCase(MOTADATA_SYSTEM) && !remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                                {
                                    HAConstants.sync(table, filter, null, user, remoteIP, HAConstants.HASyncOperation.DELETE.getName(), BackupProfile.BackupProfileType.CONFIG_DB.getName());
                                }

                                // Send audit notifications for each deleted item
                                if (!remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                                {
                                    for (var index = 0; index < items.size(); index++)
                                    {
                                        notify(remoteIP, user, REQUEST_DELETE, table, Boolean.TRUE, null, null, items.getJsonObject(index));
                                    }
                                }

                                promise.complete(records);

                            }
                            else
                            {
                                LOGGER.error(result.cause());

                                if (!remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                                {
                                    notify(remoteIP, user, REQUEST_DELETE, table, Boolean.TRUE, result.cause().getMessage(), null, null);
                                }

                                promise.fail(result.cause());
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);

                            promise.fail(exception);
                        }
                    });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }

    @Override
    public Future<JsonArray> update(String table, JsonObject filter, JsonObject record, String user, String remoteIP)
    {
        return updateAll(table, filter, record, user, remoteIP);
    }

    @Override
    public Future<JsonArray> updateAll(String table, JsonObject filter, JsonObject record, String user, String remoteIP)
    {
        var oldItems = new ArrayList<JsonObject>();

        var records = new ArrayList<Long>();

        var promise = Promise.<JsonArray>promise();

        this.get(table, filter)
                .onComplete(result ->
                {
                    if (result.succeeded())
                    {
                        if (!result.result().isEmpty())
                        {
                            try
                            {
                                var query = new StringBuilder();

                                var tuples = new ArrayList<Tuple>();

                                query.append("UPDATE ").append(table).append(" AS d SET record = v.record FROM ( VALUES ($1::BIGINT, $2::JSONB) ) AS v(id, record) WHERE d.id = v.id");

                                var items = result.result();

                                for (var index = 0; index < items.size(); index++)
                                {
                                    var item = items.getJsonObject(index);

                                    oldItems.add(item);

                                    var garbageFields = (JsonArray) record.remove(DBConstants.GARBAGE_FIELDS);

                                    removeGarbageFields(item.getLong(ID), record, item.mergeIn(record), records, garbageFields);

                                    if (!remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                                    {
                                        CommonUtil.encryptSensitiveFields(item, cipherUtil);
                                    }

                                    tuples.add(Tuple.of(item.getLong(ID), item));
                                }

                                if (!tuples.isEmpty())
                                {
                                    db.preparedQuery(query.toString())
                                            .executeBatch(tuples)
                                            .onComplete(asyncResult ->
                                            {
                                                try
                                                {
                                                    if (asyncResult.succeeded())
                                                    {
                                                        promise.complete(new JsonArray(records));

                                                        if (!remoteIP.equalsIgnoreCase(MOTADATA_SYSTEM) && !remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                                                        {
                                                            HAConstants.sync(table, filter, record, user, remoteIP, HAConstants.HASyncOperation.UPDATE_ALL.getName(), BackupProfile.BackupProfileType.CONFIG_DB.getName());
                                                        }

                                                        if (!remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                                                        {
                                                            for (var oldItem : oldItems)
                                                            {
                                                                notify(remoteIP, user, REQUEST_UPDATE, table, Boolean.TRUE, null, oldItem, record);
                                                            }
                                                        }
                                                    }
                                                    else
                                                    {
                                                        LOGGER.error(asyncResult.cause());

                                                        promise.fail(asyncResult.cause());

                                                        if (!remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                                                        {
                                                            notify(remoteIP, user, REQUEST_UPDATE, table, Boolean.FALSE, asyncResult.cause().getMessage(), null, null);
                                                        }
                                                    }
                                                }
                                                catch (Exception exception)
                                                {
                                                    LOGGER.error(exception);

                                                    promise.fail(exception);
                                                }
                                            });
                                }
                                else
                                {
                                    LOGGER.debug("request received to update empty data");

                                    promise.complete(new JsonArray(records));
                                }

                            }
                            catch (Exception exception)
                            {
                                LOGGER.error(exception);

                                promise.fail(exception);
                            }
                        }
                        else
                        {
                            var cause = "no result for query " + filter.encode();

                            LOGGER.warn(cause);

                            promise.fail(cause);

                            if (!remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                            {
                                notify(remoteIP, user, REQUEST_UPDATE, table, Boolean.FALSE, cause, null, null);
                            }
                        }
                    }
                    else
                    {
                        LOGGER.error(result.cause());

                        promise.fail(result.cause());

                        if (!remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                        {
                            notify(remoteIP, user, REQUEST_UPDATE, table, Boolean.FALSE, result.cause().getMessage(), null, null);
                        }
                    }
                });

        return promise.future();
    }

    /**
     * Retrieves records from the specified table based on query criteria.
     *
     * <p>This method supports querying by any field in the JSON record or by ID.
     * If no query is provided, it returns all records from the table. The method
     * handles both single value and array value queries with IN clause support.</p>
     *
     * @param table   The name of the table to query
     * @param filter  Query criteria as JsonObject with field name and value, or null for all records
     * @return Future containing array of matching records or error
     */
    @Override
    public Future<JsonArray> get(String table, JsonObject filter)
    {
        var items = new JsonArray();
        var promise = Promise.<JsonArray>promise();

        try
        {
            // Execute query with specific criteria
            if (filter != null && !filter.isEmpty())
            {
                var query = new StringBuilder();

                // Build query based on field type (ID vs JSON field)
                query.append("SELECT * FROM ").append(table).append(" WHERE ").append(filter.getString(DBConstants.FIELD_NAME).equalsIgnoreCase(GlobalConstants.ID) ? "id" : String.format("record->>'%s'", filter.getString(DBConstants.FIELD_NAME)));

                var tuple = buildTuple(query, filter);

                db.preparedQuery(query.toString())
                        .execute(tuple)
                        .onComplete(result ->
                        {
                            try
                            {
                                if (result.succeeded())
                                {
                                    // Extract records from result rows
                                    for (var row : result.result())
                                    {
                                        var item = row.getJsonObject(RECORD);

                                        CommonUtil.decryptSensitiveFields(item, cipherUtil);

                                        items.add(item);
                                    }

                                    promise.complete(items);
                                }
                                else if (result.cause().getMessage().contains("does not exist"))
                                {
                                    // Table doesn't exist, return empty result
                                    promise.complete(items);
                                }
                                else
                                {
                                    LOGGER.error(result.cause());

                                    promise.fail(result.cause());
                                }
                            }
                            catch (Exception exception)
                            {
                                LOGGER.error(exception);

                                promise.fail(exception);
                            }
                        });
            }
            else
            {
                // No query provided, return all records
                db.preparedQuery("SELECT * FROM " + table).execute()
                        .onComplete(result ->
                        {
                            try
                            {
                                if (result.succeeded())
                                {
                                    // Extract all records from result
                                    for (var row : result.result())
                                    {
                                        var item = row.getJsonObject(RECORD);

                                        CommonUtil.decryptSensitiveFields(item, cipherUtil);

                                        items.add(item);
                                    }

                                    promise.complete(items);
                                }
                                else if (result.cause().getMessage().contains("does not exist"))
                                {
                                    // Table doesn't exist, return empty result
                                    promise.complete(items);
                                }
                                else
                                {
                                    LOGGER.error(result.cause());

                                    promise.fail(result.cause());
                                }
                            }
                            catch (Exception exception)
                            {
                                LOGGER.error(exception);

                                promise.fail(exception);
                            }
                        });
            }
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            promise.fail(exception);
        }

        return promise.future();
    }

    /**
     * Retrieves a single record by its ID.
     *
     * @param table   The name of the table to query
     * @param id      The unique ID of the record to retrieve
     * @return Future containing the record or empty JsonObject if not found
     *//*
    @Override
    public Future<JsonObject> getById(String table, long id)
    {
        return getOneByQuery(table, new JsonObject().put(DBConstants.FIELD_NAME, GlobalConstants.ID).put(VALUE, id));
    }

    *//**
     * Retrieves the first record matching the specified query criteria.
     *
     * @param table   The name of the table to query
     * @param filter  Query criteria as JsonObject with field name and value
     * @return Future containing the first matching record or empty JsonObject if none found
     *//*
    @Override
    public Future<JsonObject> getOneByQuery(String table, JsonObject filter)
    {
        return get(table, filter)
            .map(result -> {
                if (!result.isEmpty())
                {
                    return result.getJsonObject(0);
                }
                else
                {
                    return new JsonObject();
                }
            });
    }

    *//**
     * Retrieves any single record from the specified table.
     *
     * <p>This method returns the first record found in the table without
     * any specific ordering. Useful for getting a sample record or when
     * only one record is expected to exist.</p>
     *
     * @param table   The name of the table to query
     * @return Future containing a single record or empty JsonObject if table is empty
     *//*
    @Override
    public Future<JsonObject> getOne(String table)
    {
        var promise = Promise.<JsonObject>promise();

        db.query("SELECT * FROM " + table + " LIMIT 1")
                .execute(result ->
                {
                    try
                    {
                        if (result.succeeded())
                        {
                            var iterator = result.result().iterator();

                            if (iterator.hasNext())
                            {
                                var item = iterator.next().getJsonObject(RECORD);

                                CommonUtil.decryptSensitiveFields(item, cipherUtil);

                                promise.complete(item);
                            }
                            else
                            {
                                promise.complete(new JsonObject());
                            }
                        }
                        else if (result.cause().getMessage().contains("does not exist"))
                        {
                            // Table doesn't exist, return empty record
                            promise.complete(new JsonObject());
                        }
                        else
                        {
                            LOGGER.error(result.cause());

                            promise.fail(result.cause());
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);

                        promise.fail(exception);
                    }
                });

        return promise.future();
    }

    *//**
     * Retrieves all records from the specified table.
     *
     * @param table   The name of the table to query
     * @return Future containing array of all records in the table
     *//*
    @Override
    public Future<JsonArray> getAll(String table)
    {
        return get(table, null);
    }

    *//**
     * Deletes records from the specified table based on query criteria.
     *
     * <p>This method delegates to deleteAll for consistency. System entities
     * are protected from deletion to maintain data integrity.</p>
     *
     * @param table    The name of the table to delete from
     * @param filter   Query criteria to identify records to delete
     * @param user     The username performing the operation for audit purposes
     * @param remoteIP The remote IP address for audit and security tracking
     * @return Future containing array of deleted record IDs or error
     *//*
    @Override
    public Future<JsonArray> delete(String table, JsonObject filter, String user, String remoteIP)
    {
        return deleteAll(table, filter, user, remoteIP);
    }

    *//**
     * Deletes all records matching the specified query criteria.
     *
     * <p>This method performs a bulk delete operation with protection for system entities.
     * It returns the deleted records and performs HA synchronization and audit logging.
     * System entities (marked with ENTITY_TYPE_SYSTEM) are automatically excluded from deletion.</p>
     *
     * @param table    The name of the table to delete from
     * @param filter   Query criteria to identify records to delete
     * @param user     The username performing the operation for audit purposes
     * @param remoteIP The remote IP address for audit and security tracking
     * @return Future containing array of deleted record IDs or error
     *//*
    @Override
    public Future<JsonArray> deleteAll(String table, JsonObject filter, String user, String remoteIP)
    {
        try
        {
            var items = new JsonArray();

            var records = new JsonArray();

            var query = new StringBuilder();

            // Build delete query with system entity protection
            query.append("DELETE FROM ").append(table).append(" WHERE ").append(filter.getString(DBConstants.FIELD_NAME).equalsIgnoreCase(GlobalConstants.ID) ? "id" : String.format("record->>'%s' != '%s' AND record->>'%s'", DBConstants.FIELD_TYPE, DBConstants.ENTITY_TYPE_SYSTEM, filter.getString(DBConstants.FIELD_NAME)));

            var tuple = buildTuple(query, filter);

            query.append(" RETURNING *");

            db.preparedQuery(query.toString())
                    .execute(tuple, result ->
                    {
                        try
                        {
                            if (result.succeeded())
                            {
                                // Collect deleted records and their IDs
                                for (var row : result.result())
                                {
                                    items.add(row.getJsonObject(RECORD));

                                    records.add(row.getLong(ID));
                                }

                                // Sync with HA cluster if not system or observer operation
                                if (!remoteIP.equalsIgnoreCase(MOTADATA_SYSTEM) && !remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                                {
                                    HAConstants.sync(table, filter, null, user, remoteIP, HAConstants.HASyncOperation.DELETE.getName(), BackupProfile.BackupProfileType.CONFIG_DB.getName());
                                }

                                // Send audit notifications for each deleted item
                                if (!remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                                {
                                    for (var index = 0; index < items.size(); index++)
                                    {
                                        notify(remoteIP, user, REQUEST_DELETE, table, Boolean.TRUE, null, null, items.getJsonObject(index));
                                    }
                                }

                                handler.handle(Future.succeededFuture(records));
                            }
                            else
                            {
                                LOGGER.error(result.cause());

                                if (!remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                                {
                                    notify(remoteIP, user, REQUEST_DELETE, table, Boolean.TRUE, result.cause().getMessage(), null, null);
                                }

                                handler.handle(Future.failedFuture(result.cause()));
                            }
                        }
                        catch (Exception exception)
                        {
                            LOGGER.error(exception);

                            handler.handle(Future.failedFuture(exception));
                        }
                    });
        }
        catch (Exception exception)
        {
            LOGGER.error(exception);

            handler.handle(Future.failedFuture(exception));
        }

        return this;
    }

    *//**
     * Drops the specified table completely from the database.
     *
     * <p>This method checks if the table exists before attempting to drop it.
     * It performs HA synchronization and audit logging for the drop operation.
     * Use with caution as this operation is irreversible.</p>
     *
     * @param table   The name of the table to drop
     * @param handler Handler to receive success confirmation or error
     * @return This service instance for method chaining
     *//*
    @Override
    public ConfigDBService drop(String table, Handler<AsyncResult<Void>> handler)
    {
        // Check if table exists before attempting to drop
        db.preparedQuery("SELECT EXISTS (SELECT 1 FROM pg_tables  WHERE tablename = '" + table + "' )")
                .execute(result ->
                {
                    try
                    {
                        if (result.succeeded())
                        {
                            var iterator = result.result().iterator();

                            if (iterator.hasNext() && iterator.next().getBoolean(0))
                            {
                                // Table exists, proceed with drop
                                db.preparedQuery("DROP TABLE " + table).execute(asyncResult ->
                                {
                                    if (asyncResult.succeeded())
                                    {
                                        // Sync with HA cluster
                                        HAConstants.sync(table, null, null, SYSTEM_USER, SYSTEM_REMOTE_ADDRESS, HAConstants.HASyncOperation.DROP.getName(), BackupProfile.BackupProfileType.CONFIG_DB.getName());

                                        // Send audit notification
                                        notify(SYSTEM_REMOTE_ADDRESS, SYSTEM_USER, REQUEST_DROP, table, Boolean.TRUE, null, null, null);

                                        handler.handle(Future.succeededFuture());
                                    }
                                    else
                                    {
                                        LOGGER.error(asyncResult.cause());

                                        handler.handle(Future.failedFuture(asyncResult.cause()));
                                    }
                                });
                            }
                            else
                            {
                                // Table doesn't exist, consider operation successful
                                handler.handle(Future.succeededFuture());
                            }
                        }
                        else
                        {
                            LOGGER.error(result.cause());

                            handler.handle(Future.failedFuture(result.cause()));
                        }
                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);

                        handler.handle(Future.failedFuture(exception));
                    }
                });

        return this;
    }

    *//**
     * Updates records in the specified table based on query criteria.
     *
     * <p>This method delegates to updateAll for consistency. It merges the provided
     * record fields with existing records matching the query.</p>
     *
     * @param table    The name of the table to update
     * @param filter   Query criteria to identify records to update
     * @param record   JSON object containing fields to update
     * @param user     The username performing the operation for audit purposes
     * @param remoteIP The remote IP address for audit and security tracking
     * @param handler  Handler to receive array of updated record IDs or error
     * @return This service instance for method chaining
     *//*
    @Override
    public Future<JsonArray> update(String table, JsonObject filter, JsonObject record, String user, String remoteIP)
    {
        return updateAll(table, filter, record, user, remoteIP);
    }

    *//**
     * Performs an upsert operation (insert or update) for a single record.
     *
     * <p>This method is strictly used during configuration database initialization.
     * It inserts the record if it doesn't exist, or updates it if it does exist
     * based on the record ID.</p>
     *
     * @param table    The name of the table for the upsert operation
     * @param record   The JSON record to upsert
     * @param user     The username performing the operation for audit purposes
     * @param remoteIP The remote IP address for audit and security tracking
     * @param handler  Handler to receive the record ID or error
     * @return This service instance for method chaining
     *//*
    @Override
    public Future<Long> upsert(String table, JsonObject record, String user, String remoteIP)
    {
        return upsertAll(table, new JsonArray().add(record), user, remoteIP)
            .map(result -> result.getLong(0));
    }

    @Override
    public ConfigDBService upsertAll(String table, JsonArray records, String user, String remoteIP, Handler<AsyncResult<JsonArray>> handler)
    {
        var items = new JsonArray();

        db.query("CREATE TABLE IF NOT EXISTS " + table + " (id BIGINT PRIMARY KEY, record JSONB)").execute(result ->
        {
            try
            {
                if (result.succeeded())
                {
                    var recordIds = new JsonArray();

                    if (records != null)
                    {
                        var tuples = new ArrayList<Tuple>();

                        for (var index = 0; index < records.size(); index++)
                        {
                            var record = records.getJsonObject(index);

                            if (!record.containsKey(DBConstants.FIELD_TYPE))
                            {
                                record.put(DBConstants.FIELD_TYPE, DBConstants.ENTITY_TYPE_USER);
                            }

                            var id = record.containsKey(GlobalConstants.ID) ? record.getLong(GlobalConstants.ID) : CommonUtil.newId();

                            tuples.add(Tuple.of(id, record.put(GlobalConstants.ID, id)));

                            recordIds.add(id);

                            if (!remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                            {
                                CommonUtil.encryptSensitiveFields(record, cipherUtil);
                            }

                            items.add(record);
                        }

                        if (!tuples.isEmpty())
                        {
                            db.preparedQuery("INSERT INTO " + table + " (id, record) VALUES ($1, $2) ON CONFLICT (id) DO UPDATE SET record = " + table + ".record || EXCLUDED.record")
                                    .executeBatch(tuples, asyncResult ->
                                    {
                                        try
                                        {
                                            if (asyncResult.succeeded())
                                            {
                                                handler.handle(Future.succeededFuture(recordIds));

                                                if (!remoteIP.equalsIgnoreCase(MOTADATA_SYSTEM) && !remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                                                {
                                                    HAConstants.sync(table, items, user, HAConstants.HASyncOperation.UPSERT.getName(), BackupProfile.BackupProfileType.CONFIG_DB.getName());
                                                }

                                                if (!remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                                                {
                                                    for (var index = 0; index < items.size(); index++)
                                                    {
                                                        notify(remoteIP, user, REQUEST_CREATE, table, Boolean.TRUE, null, null, items.getJsonObject(index));
                                                    }
                                                }
                                            }
                                            else
                                            {
                                                LOGGER.error(asyncResult.cause());

                                                handler.handle(Future.failedFuture(asyncResult.cause()));

                                                notify(remoteIP, user, REQUEST_CREATE, table, Boolean.FALSE, asyncResult.cause().getMessage(), null, null);
                                            }
                                        }
                                        catch (Exception exception)
                                        {
                                            LOGGER.error(exception);

                                            handler.handle(Future.failedFuture(exception));
                                        }
                                    });
                        }
                        else
                        {
                            LOGGER.debug("request received to insert empty data");

                            handler.handle(Future.succeededFuture(recordIds));
                        }

                    }
                    else // this is the case when no entries exist, but we need to create the table (ex: agent.json)
                    {
                        handler.handle(Future.succeededFuture(recordIds));
                    }
                }
                else
                {
                    LOGGER.error(result.cause());

                    handler.handle(Future.failedFuture(result.cause()));

                    notify(remoteIP, user, REQUEST_CREATE, table, Boolean.FALSE, result.cause().getMessage(), null, null);
                }
            }
            catch (Exception exception)
            {
                LOGGER.error(exception);

                handler.handle(Future.failedFuture(exception));
            }
        });

        return this;
    }

    @Override
    public ConfigDBService updateAll(String table, JsonObject filter, JsonObject record, String user, String remoteIP, Handler<AsyncResult<JsonArray>> handler)
    {
        var oldItems = new ArrayList<JsonObject>();

        var records = new ArrayList<Long>();

        this.get(table, filter, result ->
        {
            if (result.succeeded())
            {
                if (!result.result().isEmpty())
                {
                    try
                    {
                        var query = new StringBuilder();

                        var tuples = new ArrayList<Tuple>();

                        query.append("UPDATE ").append(table).append(" AS d SET record = v.record FROM ( VALUES ($1::BIGINT, $2::JSONB) ) AS v(id, record) WHERE d.id = v.id");

                        var items = result.result();

                        for (var index = 0; index < items.size(); index++)
                        {
                            var item = items.getJsonObject(index);

                            oldItems.add(item);

                            var garbageFields = (JsonArray) record.remove(DBConstants.GARBAGE_FIELDS);

                            removeGarbageFields(item.getLong(ID), record, item.mergeIn(record), records, garbageFields);

                            if (!remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                            {
                                CommonUtil.encryptSensitiveFields(item, cipherUtil);
                            }

                            tuples.add(Tuple.of(item.getLong(ID), item));
                        }

                        if (!tuples.isEmpty())
                        {
                            db.preparedQuery(query.toString())
                                    .executeBatch(tuples, asyncResult ->
                                    {
                                        try
                                        {
                                            if (asyncResult.succeeded())
                                            {
                                                handler.handle(Future.succeededFuture(new JsonArray(records)));

                                                if (!remoteIP.equalsIgnoreCase(MOTADATA_SYSTEM) && !remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                                                {
                                                    HAConstants.sync(table, filter, record, user, remoteIP, HAConstants.HASyncOperation.UPDATE_ALL.getName(), BackupProfile.BackupProfileType.CONFIG_DB.getName());
                                                }

                                                if (!remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                                                {
                                                    for (var oldItem : oldItems)
                                                    {
                                                        notify(remoteIP, user, REQUEST_UPDATE, table, Boolean.TRUE, null, oldItem, record);
                                                    }
                                                }
                                            }
                                            else
                                            {
                                                LOGGER.error(asyncResult.cause());

                                                handler.handle(Future.failedFuture(asyncResult.cause()));

                                                if (!remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                                                {
                                                    notify(remoteIP, user, REQUEST_UPDATE, table, Boolean.FALSE, asyncResult.cause().getMessage(), null, null);
                                                }
                                            }
                                        }
                                        catch (Exception exception)
                                        {
                                            LOGGER.error(exception);

                                            handler.handle(Future.failedFuture(exception));
                                        }
                                    });
                        }
                        else
                        {
                            LOGGER.debug("request received to update empty data");

                            handler.handle(Future.succeededFuture(new JsonArray(records)));
                        }

                    }
                    catch (Exception exception)
                    {
                        LOGGER.error(exception);

                        handler.handle(Future.failedFuture(exception));
                    }
                }
                else
                {
                    var cause = "no result for query " + filter.encode();

                    LOGGER.warn(cause);

                    handler.handle(Future.failedFuture(cause));

                    if (!remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                    {
                        notify(remoteIP, user, REQUEST_UPDATE, table, Boolean.FALSE, cause, null, null);
                    }
                }
            }
            else
            {
                LOGGER.error(result.cause());

                handler.handle(Future.failedFuture(result.cause()));

                if (!remoteIP.equalsIgnoreCase(MOTADATA_OBSERVER))
                {
                    notify(remoteIP, user, REQUEST_UPDATE, table, Boolean.FALSE, result.cause().getMessage(), null, null);
                }
            }
        });

        return this;
    }*/

    /**
     * Builds a Tuple for prepared statement parameters and appends the appropriate WHERE clause.
     *
     * <p>This utility method handles both single value and array value queries. For array values,
     * it generates an IN clause with multiple parameters. For single values, it generates an
     * equality comparison. It also handles the difference between ID fields and JSON record fields.</p>
     *
     * @param query  StringBuilder to append the WHERE clause condition to
     * @param filter JsonObject containing the field name and value(s) to query
     * @return Tuple containing the parameter values for the prepared statement
     */
    private Tuple buildTuple(StringBuilder query, JsonObject filter)
    {
        Tuple tuple;

        var value = filter.getValue(VALUE);

        if (value instanceof JsonArray)
        {
            // Handle array values with IN clause
            tuple = filter.getString(DBConstants.FIELD_NAME).equalsIgnoreCase(GlobalConstants.ID) ?
                    Tuple.from(((JsonArray) value).getList()) :
                    Tuple.from(((JsonArray) value).getList().stream().map(CommonUtil::getString).toList());

            // Generate IN clause with parameter placeholders
            query.append(String.format(" IN (%s)", IntStream.range(0, ((JsonArray) value).size())
                    .mapToObj(i -> "$" + (i + 1))
                    .collect(Collectors.joining(","))));
        }
        else
        {
            // Handle single value with equality comparison
            tuple = filter.getString(DBConstants.FIELD_NAME).equalsIgnoreCase(GlobalConstants.ID) ?
                    Tuple.of(value) :
                    Tuple.of(value.toString());

            query.append("= $1");
        }

        return tuple;
    }

    /**
     * Sends audit notification events for database operations.
     *
     * <p>This method creates and sends audit events to the event bus for tracking
     * all database operations. It includes user information, operation details,
     * and any error information or property changes.</p>
     *
     * @param remoteIP The remote IP address of the operation initiator
     * @param user     The username performing the operation
     * @param request  The type of request (CREATE, UPDATE, DELETE, etc.)
     * @param table    The name of the table being operated on
     * @param status   Whether the operation was successful
     * @param cause    Error message if the operation failed, null otherwise
     * @param oldProps Previous property values for update operations, null otherwise
     * @param newProps New property values for create/update operations, null otherwise
     */
    private void notify(String remoteIP, String user, String request, String table, boolean status, String cause, JsonObject oldProps, JsonObject newProps)
    {
        var event = new JsonObject().put(REMOTE_ADDRESS, remoteIP).put(USER_NAME, user).put(ENTITY_TABLE, table)
                .put(REQUEST, request).put(STATUS, status);

        if (cause != null)
        {
            event.put(ERROR, cause);
        }

        if (oldProps != null && !oldProps.isEmpty())
        {
            event.put(CONFIG_OLD_PROPS, oldProps);
        }

        if (newProps != null && !newProps.isEmpty())
        {
            event.put(CONFIG_UPDATED_PROPS, newProps);
        }

        // Send audit event for all operations except DROP
        if (!request.equalsIgnoreCase(REQUEST_DROP))
        {
            vertx.eventBus().send(EVENT_AUDIT, event);
        }
    }

    /**
     * Removes specified garbage fields from a record during update operations.
     *
     * <p>This method removes unwanted fields from records and marks them as empty
     * in the update record. It also adds the record ID to the processed records list.</p>
     *
     * @param id            The ID of the record being processed
     * @param updatedItem   The update record to mark removed fields in
     * @param item          The original record to remove fields from
     * @param records       List to add the processed record ID to
     * @param garbageFields Array of field names to remove, null if none
     */
    private void removeGarbageFields(long id, JsonObject updatedItem, JsonObject item, List<Long> records, JsonArray garbageFields)
    {
        if (garbageFields != null)
        {
            for (var index = 0; index < garbageFields.size(); index++)
            {
                if (item.containsKey(garbageFields.getString(index)))
                {
                    item.remove(garbageFields.getString(index));

                    updatedItem.put(garbageFields.getString(index), EMPTY_VALUE);
                }
            }
        }

        records.add(id);
    }
}
